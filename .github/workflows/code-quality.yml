name: Code Quality

on:
  pull_request:
    branches:
      - main
      - release
    types: [opened, synchronize, reopened]

env:
  DOTNET_VERSION: '9.0.x'

jobs:
  code-analysis:
    name: Code Quality Analysis
    runs-on: ubuntu-latest

    permissions:
      contents: read
      security-events: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Shallow clones should be disabled for better analysis

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Cache NuGet packages
      uses: actions/cache@v4
      with:
        path: ~/.nuget/packages
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj', '**/Directory.Packages.props') }}
        restore-keys: |
          ${{ runner.os }}-nuget-

    - name: Restore dependencies
      run: dotnet restore
      env:
        NUGET_USERNAME: ${{ secrets.NUGET_USERNAME }}
        NUGET_TOKEN: ${{ secrets.NUGET_TOKEN }}

    - name: Build for analysis
      run: dotnet build --configuration Release --no-restore

    - name: Run .NET Format check
      id: format-check
      continue-on-error: true
      run: |
        echo "## 🎨 Code Formatting Analysis" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        if dotnet format --verify-no-changes --verbosity diagnostic 2>&1 | tee format-output.txt; then
          echo "✅ **Code Formatting**: All files are properly formatted" >> $GITHUB_STEP_SUMMARY
          echo "format-status=success" >> $GITHUB_OUTPUT
        else
          echo "❌ **Code Formatting**: Issues found" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          # Count formatting issues
          ISSUES_COUNT=$(grep -c "error WHITESPACE" format-output.txt || echo "0")
          FILES_COUNT=$(grep "Formatted code file" format-output.txt | wc -l || echo "0")

          echo "- **Files with issues**: $FILES_COUNT" >> $GITHUB_STEP_SUMMARY
          echo "- **Total formatting issues**: $ISSUES_COUNT" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Fix with**: \`dotnet format\`" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          echo "format-status=failed" >> $GITHUB_OUTPUT
        fi

    - name: Security scan with .NET Security
      id: security-scan
      continue-on-error: true
      run: |
        echo "## 🔒 Security Analysis" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        dotnet list package --vulnerable --include-transitive 2>&1 | tee security-report.txt

        if grep -q "has the following vulnerable packages" security-report.txt; then
          echo "❌ **Security Scan**: Vulnerable packages found" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Vulnerable packages detected:**" >> $GITHUB_STEP_SUMMARY
          echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
          grep -A 10 "has the following vulnerable packages" security-report.txt >> $GITHUB_STEP_SUMMARY || true
          echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
          echo "security-status=failed" >> $GITHUB_OUTPUT
        else
          echo "✅ **Security Scan**: No vulnerable packages found" >> $GITHUB_STEP_SUMMARY
          echo "security-status=success" >> $GITHUB_OUTPUT
        fi

    - name: Dockerfile security scan
      id: dockerfile-scan
      continue-on-error: true
      uses: hadolint/hadolint-action@v3.1.0
      with:
        dockerfile: Dockerfile
        format: sarif
        output-file: hadolint-results.sarif
        no-fail: true

    - name: Upload Dockerfile scan results
      if: always()
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: hadolint-results.sarif
        wait-for-processing: true

    - name: Generate Quality Summary
      if: always()
      run: |
        echo "## 📊 Code Quality Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        FORMAT_STATUS="${{ steps.format-check.outputs.format-status }}"
        SECURITY_STATUS="${{ steps.security-scan.outputs.security-status }}"
        DOCKERFILE_STATUS="${{ steps.dockerfile-scan.outcome }}"

        # Overall quality score
        SCORE=0
        TOTAL=3

        if [ "$FORMAT_STATUS" == "success" ]; then
          SCORE=$((SCORE + 1))
          echo "✅ **Code Formatting**: PASSED" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ **Code Formatting**: FAILED" >> $GITHUB_STEP_SUMMARY
        fi

        if [ "$SECURITY_STATUS" == "success" ]; then
          SCORE=$((SCORE + 1))
          echo "✅ **Security Scan**: PASSED" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ **Security Scan**: FAILED" >> $GITHUB_STEP_SUMMARY
        fi

        if [ "$DOCKERFILE_STATUS" == "success" ]; then
          SCORE=$((SCORE + 1))
          echo "✅ **Dockerfile Lint**: PASSED" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ **Dockerfile Lint**: FAILED" >> $GITHUB_STEP_SUMMARY
        fi

        echo "" >> $GITHUB_STEP_SUMMARY
        PERCENTAGE=$((SCORE * 100 / TOTAL))
        echo "**Quality Score**: $SCORE/$TOTAL ($PERCENTAGE%)" >> $GITHUB_STEP_SUMMARY

        if [ $SCORE -eq $TOTAL ]; then
          echo "🎉 **All quality checks passed!**" >> $GITHUB_STEP_SUMMARY
        else
          echo "⚠️ **Some quality issues found. Please review and fix.**" >> $GITHUB_STEP_SUMMARY
        fi
