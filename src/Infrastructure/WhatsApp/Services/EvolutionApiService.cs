using System.Text;
using System.Text.Json;
using Domain.WhatsApp.DTOs;
using Domain.WhatsApp.Interfaces;
using Infrastructure.WhatsApp.Configuration;
using Microsoft.Extensions.Options;

namespace Infrastructure.WhatsApp.Services;

public class EvolutionApiService : IEvolutionApiService
{
    private readonly HttpClient _httpClient;
    private readonly EvolutionApiSettings _settings;
    private readonly JsonSerializerOptions _jsonOptions;

    public EvolutionApiService(HttpClient httpClient, IOptions<EvolutionApiSettings> settings)
    {
        _httpClient = httpClient;
        _settings = settings.Value;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true,
        };
    }

    public async Task<SendMessageResponse> SendTextMessageAsync(
        string instanceName,
        SendMessageRequest request,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            _httpClient.DefaultRequestHeaders.Add("apikey", _settings.GlobalApiKey);
            var url = $"{_settings.BaseUrl}message/sendText/{instanceName}";
            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(url, content, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<dynamic>(responseContent);
                return new SendMessageResponse { Success = true, MessageId = result?.ToString() };
            }

            return new SendMessageResponse
            {
                Success = false,
                Error = $"HTTP {response.StatusCode}: {responseContent}",
            };
        }
        catch (Exception ex)
        {
            return new SendMessageResponse { Success = false, Error = ex.Message };
        }
    }

    public async Task<QrCodeResponse> GetQrCodeAsync(
        string instanceName,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            var url = $"{_settings.BaseUrl}instance/connect/{instanceName}";

            _httpClient.DefaultRequestHeaders.Add("apikey", _settings.GlobalApiKey);
            var response = await _httpClient.GetAsync(url, cancellationToken);

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            if (response.IsSuccessStatusCode)
            {
                using var document = JsonDocument.Parse(responseContent);
                var root = document.RootElement;

                if (root.TryGetProperty("base64", out var base64Element))
                {
                    return new QrCodeResponse
                    {
                        Success = true,
                        Base64 = base64Element.GetString(),
                        Code = base64Element.GetString(),
                    };
                }
            }

            return new QrCodeResponse
            {
                Success = false,
                Error = $"HTTP {response.StatusCode}: {responseContent}",
            };
        }
        catch (Exception ex)
        {
            return new QrCodeResponse { Success = false, Error = ex.Message };
        }
    }

    public async Task<ConnectionStatusResponse> GetConnectionStatusAsync(
        string instanceName,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            var url = $"{_settings.BaseUrl}instance/connectionState/{instanceName}";
            _httpClient.DefaultRequestHeaders.Add("apikey", _settings.GlobalApiKey);
            var response = await _httpClient.GetAsync(url, cancellationToken);

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                using var document = JsonDocument.Parse(responseContent);
                var root = document.RootElement;

                var status = root.TryGetProperty("state", out var stateElement)
                    ? stateElement.GetString()
                    : "unknown";
                var isConnected = status?.ToLower() == "open";

                return new ConnectionStatusResponse
                {
                    Success = true,
                    Status = status,
                    IsConnected = isConnected,
                };
            }

            return new ConnectionStatusResponse
            {
                Success = false,
                Error = $"HTTP {response.StatusCode}: {responseContent}",
            };
        }
        catch (Exception ex)
        {
            return new ConnectionStatusResponse { Success = false, Error = ex.Message };
        }
    }
}
