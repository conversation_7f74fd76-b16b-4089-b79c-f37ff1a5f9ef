using Domain.WhatsApp.DTOs;
using Domain.WhatsApp.Interfaces;
using Microsoft.Extensions.Logging;

namespace Infrastructure.WhatsApp.Services;

public class WhatsAppIntegrationService : IWhatsAppIntegrationService
{
    private readonly IEvolutionApiService _evolutionApiService;
    private readonly ILogger<WhatsAppIntegrationService> _logger;

    public WhatsAppIntegrationService(
        IEvolutionApiService evolutionApiService,
        ILogger<WhatsAppIntegrationService> logger)
    {
        _evolutionApiService = evolutionApiService;
        _logger = logger;
    }

    public async Task<SendMessageResponse> SendMessageAsync(string instanceName, SendMessageRequest request, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Sending WhatsApp message to {Number} via instance {InstanceName}", request.Number, instanceName);

        // Validações básicas
        if (string.IsNullOrWhiteSpace(instanceName))
        {
            return new SendMessageResponse
            {
                Success = false,
                Error = "Instance name is required"
            };
        }

        if (string.IsNullOrWhiteSpace(request.Number))
        {
            return new SendMessageResponse
            {
                Success = false,
                Error = "Phone number is required"
            };
        }

        if (string.IsNullOrWhiteSpace(request.Text))
        {
            return new SendMessageResponse
            {
                Success = false,
                Error = "Message text is required"
            };
        }

        try
        {
            var result = await _evolutionApiService.SendTextMessageAsync(instanceName, request, cancellationToken);

            if (result.Success)
            {
                _logger.LogInformation("WhatsApp message sent successfully to {Number}", request.Number);
            }
            else
            {
                _logger.LogWarning("Failed to send WhatsApp message to {Number}: {Error}", request.Number, result.Error);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending WhatsApp message to {Number}", request.Number);
            return new SendMessageResponse
            {
                Success = false,
                Error = $"Internal error: {ex.Message}"
            };
        }
    }

    public async Task<QrCodeResponse> GetQrCodeAsync(string instanceName, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting QR code for WhatsApp instance {InstanceName}", instanceName);

        if (string.IsNullOrWhiteSpace(instanceName))
        {
            return new QrCodeResponse
            {
                Success = false,
                Error = "Instance name is required"
            };
        }

        try
        {
            var result = await _evolutionApiService.GetQrCodeAsync(instanceName, cancellationToken);

            if (result.Success)
            {
                _logger.LogInformation("QR code retrieved successfully for instance {InstanceName}", instanceName);
            }
            else
            {
                _logger.LogWarning("Failed to get QR code for instance {InstanceName}: {Error}", instanceName, result.Error);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting QR code for instance {InstanceName}", instanceName);
            return new QrCodeResponse
            {
                Success = false,
                Error = $"Internal error: {ex.Message}"
            };
        }
    }

    public async Task<ConnectionStatusResponse> GetConnectionStatusAsync(string instanceName, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting connection status for WhatsApp instance {InstanceName}", instanceName);

        if (string.IsNullOrWhiteSpace(instanceName))
        {
            return new ConnectionStatusResponse
            {
                Success = false,
                Error = "Instance name is required"
            };
        }

        try
        {
            var result = await _evolutionApiService.GetConnectionStatusAsync(instanceName, cancellationToken);

            if (result.Success)
            {
                _logger.LogInformation("Connection status retrieved successfully for instance {InstanceName}: {Status}",
                    instanceName, result.Status);
            }
            else
            {
                _logger.LogWarning("Failed to get connection status for instance {InstanceName}: {Error}",
                    instanceName, result.Error);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting connection status for instance {InstanceName}", instanceName);
            return new ConnectionStatusResponse
            {
                Success = false,
                Error = $"Internal error: {ex.Message}"
            };
        }
    }
}
