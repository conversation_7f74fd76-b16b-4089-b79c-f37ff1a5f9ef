using Domain.WhatsApp.Interfaces;
using Infrastructure.WhatsApp.Extensions;
using Infrastructure.WhatsApp.Services;
using Microsoft.Extensions.Hosting;
namespace Microsoft.Extensions.DependencyInjection;

public static class DependencyInjection
{
    public static void AddInfrastructureServices(this IHostApplicationBuilder builder)
    {
        builder.Services.AddScoped<IEvolutionApiService, EvolutionApiService>();
        builder.Services.AddScoped<IWhatsAppIntegrationService, WhatsAppIntegrationService>();
        builder.Services.AddWhatsAppServices(builder.Configuration);

    }
}
