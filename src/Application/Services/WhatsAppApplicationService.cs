using Domain.WhatsApp.DTOs;
using Domain.WhatsApp.Interfaces;

namespace Application.WhatsApp.Services;

public class WhatsAppApplicationService : IWhatsAppApplicationService
{
    private readonly IWhatsAppIntegrationService _whatsAppIntegrationService;

    public WhatsAppApplicationService(IWhatsAppIntegrationService whatsAppIntegrationService)
    {
        _whatsAppIntegrationService = whatsAppIntegrationService;
    }

    public async Task<SendMessageResponse> SendMessageAsync(
        string instanceName,
        SendMessageRequest request,
        CancellationToken cancellationToken = default
    )
    {
        return await _whatsAppIntegrationService.SendMessageAsync(
            instanceName,
            request,
            cancellationToken
        );
    }

    public async Task<QrCodeResponse> GetQrCodeAsync(
        string instanceName,
        CancellationToken cancellationToken = default
    )
    {
        return await _whatsAppIntegrationService.GetQrCodeAsync(instanceName, cancellationToken);
    }

    public async Task<ConnectionStatusResponse> GetConnectionStatusAsync(
        string instanceName,
        CancellationToken cancellationToken = default
    )
    {
        return await _whatsAppIntegrationService.GetConnectionStatusAsync(
            instanceName,
            cancellationToken
        );
    }
}
