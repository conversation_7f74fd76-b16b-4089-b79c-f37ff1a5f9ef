using Domain.WhatsApp.DTOs;

namespace Domain.WhatsApp.Interfaces;

public interface IWhatsAppIntegrationService
{
    Task<SendMessageResponse> SendMessageAsync(string instanceName, SendMessageRequest request, CancellationToken cancellationToken = default);
    Task<QrCodeResponse> GetQrCodeAsync(string instanceName, CancellationToken cancellationToken = default);
    Task<ConnectionStatusResponse> GetConnectionStatusAsync(string instanceName, CancellationToken cancellationToken = default);
}
